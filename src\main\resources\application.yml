server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: muzaidah-platform
  
  # Database Configuration
  datasource:
    url: jdbc:h2:mem:muzaidah
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  # JPA Configuration
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
  
  # H2 Console (for development)
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
  
  # Mail Configuration (for notifications)
  mail:
    host: smtp.gmail.com
    port: 587
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:muzaidahSecretKeyForJWTTokenGeneration2023}
  expiration: 86400000 # 24 hours

# File Storage Configuration
file:
  upload-dir: ./uploads/images

# Application Configuration
app:
  cors:
    allowed-origins: "http://localhost:3000,http://localhost:8080"
  
  # Points System Configuration
  points:
    successful-deal: 20
    new-product: 10
    referral: 15
    positive-review: 5
    king-threshold: 1000

# Logging Configuration
logging:
  level:
    com.muzaidah: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/muzaidah.log

# Management Endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
