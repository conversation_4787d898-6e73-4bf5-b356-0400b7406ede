package com.muzaidah.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Product entity representing items for auction or barter
 */
@Entity
@Table(name = "products")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class Product {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "عنوان المنتج مطلوب")
    @Column(nullable = false)
    private String title;

    @NotBlank(message = "وصف المنتج مطلوب")
    @Column(columnDefinition = "TEXT")
    private String description;

    @NotNull(message = "فئة المنتج مطلوبة")
    @Enumerated(EnumType.STRING)
    private Category category;

    @NotNull(message = "نوع العملية مطلوب")
    @Enumerated(EnumType.STRING)
    private OperationType operationType;

    // Auction fields
    @Positive(message = "السعر الابتدائي يجب أن يكون أكبر من صفر")
    private BigDecimal startPrice;

    @Positive(message = "سعر الإغلاق يجب أن يكون أكبر من صفر")
    private BigDecimal closePrice;

    private BigDecimal currentPrice;

    // Barter fields
    @Column(columnDefinition = "TEXT")
    private String barterPreferences;

    // Location and timing
    private String location;

    @NotNull(message = "تاريخ انتهاء العرض مطلوب")
    private LocalDateTime endDate;

    @Enumerated(EnumType.STRING)
    private ProductStatus status = ProductStatus.ACTIVE;

    // Images
    @ElementCollection
    @CollectionTable(name = "product_images", joinColumns = @JoinColumn(name = "product_id"))
    @Column(name = "image_url")
    private List<String> images = new ArrayList<>();

    // Statistics
    @Column(nullable = false)
    private Integer viewCount = 0;

    @Column(nullable = false)
    private Integer bidCount = 0;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "owner_id", nullable = false)
    private User owner;

    @OneToMany(mappedBy = "product", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Bid> bids = new ArrayList<>();

    @OneToMany(mappedBy = "product", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<BarterOffer> barterOffers = new ArrayList<>();

    @OneToOne(mappedBy = "product", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Deal deal;

    // Audit fields
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    private LocalDateTime updatedAt;

    // Enums
    public enum Category {
        ELECTRONICS("إلكترونيات"),
        CLOTHING("ملابس"),
        FURNITURE("أثاث"),
        SERVICES("خدمات"),
        BOOKS("كتب"),
        SPORTS("رياضة"),
        AUTOMOTIVE("سيارات"),
        OTHER("أخرى");

        private final String arabicName;

        Category(String arabicName) {
            this.arabicName = arabicName;
        }

        public String getArabicName() {
            return arabicName;
        }
    }

    public enum OperationType {
        AUCTION("مزايدة"),
        BARTER("مقايضة"),
        BOTH("مزايدة ومقايضة");

        private final String arabicName;

        OperationType(String arabicName) {
            this.arabicName = arabicName;
        }

        public String getArabicName() {
            return arabicName;
        }
    }

    public enum ProductStatus {
        ACTIVE("نشط"),
        SOLD("مباع"),
        BARTERED("تم تبديله"),
        EXPIRED("منتهي الصلاحية"),
        CANCELLED("ملغي");

        private final String arabicName;

        ProductStatus(String arabicName) {
            this.arabicName = arabicName;
        }

        public String getArabicName() {
            return arabicName;
        }
    }

    // Helper methods
    public void incrementViewCount() {
        this.viewCount++;
    }

    public void incrementBidCount() {
        this.bidCount++;
    }

    public boolean isAuctionType() {
        return operationType == OperationType.AUCTION || operationType == OperationType.BOTH;
    }

    public boolean isBarterType() {
        return operationType == OperationType.BARTER || operationType == OperationType.BOTH;
    }

    public boolean isExpired() {
        return LocalDateTime.now().isAfter(endDate);
    }

    public boolean isActive() {
        return status == ProductStatus.ACTIVE && !isExpired();
    }

    public Bid getHighestBid() {
        return bids.stream()
                .filter(bid -> bid.getStatus() == Bid.BidStatus.ACTIVE)
                .max((b1, b2) -> b1.getAmount().compareTo(b2.getAmount()))
                .orElse(null);
    }

    public boolean hasReachedClosePrice() {
        if (closePrice == null || currentPrice == null) {
            return false;
        }
        return currentPrice.compareTo(closePrice) >= 0;
    }
}
