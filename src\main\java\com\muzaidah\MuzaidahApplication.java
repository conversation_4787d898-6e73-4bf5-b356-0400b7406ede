package com.muzaidah;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Muzaidah Platform - Smart Auction and Barter System
 * 
 * Main application class that bootstraps the Spring Boot application.
 * 
 * Features:
 * - Auction and Barter system
 * - Points and ranking system
 * - Real-time notifications
 * - File upload for product images
 * - JWT authentication
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableAsync
@EnableScheduling
public class MuzaidahApplication {

    public static void main(String[] args) {
        SpringApplication.run(MuzaidahApplication.class, args);
        
        System.out.println("🎉 Muzaidah Platform Started Successfully!");
        System.out.println("🌐 Access the application at: http://localhost:8080");
        System.out.println("🗄️  H2 Database Console: http://localhost:8080/api/h2-console");
        System.out.println("📊 Health Check: http://localhost:8080/api/actuator/health");
        System.out.println("📚 API Documentation will be available at: http://localhost:8080/api/swagger-ui.html");
    }
}
