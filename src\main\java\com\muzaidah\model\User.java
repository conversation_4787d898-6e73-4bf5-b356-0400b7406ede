package com.muzaidah.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * User entity representing platform users
 */
@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "اسم المستخدم مطلوب")
    @Size(min = 3, max = 50, message = "اسم المستخدم يجب أن يكون بين 3 و 50 حرف")
    @Column(unique = true)
    private String username;

    @NotBlank(message = "البريد الإلكتروني مطلوب")
    @Email(message = "البريد الإلكتروني غير صحيح")
    @Column(unique = true)
    private String email;

    @NotBlank(message = "كلمة المرور مطلوبة")
    @Size(min = 6, message = "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
    private String password;

    @NotBlank(message = "الاسم الكامل مطلوب")
    private String fullName;

    private String phoneNumber;
    
    private String location;
    
    private String profileImage;

    @Column(columnDefinition = "TEXT")
    private String bio;

    // Points and Ranking System
    @Column(nullable = false)
    private Integer points = 0;

    @Enumerated(EnumType.STRING)
    private UserRank rank = UserRank.BEGINNER;

    @Column(nullable = false)
    private Integer successfulDeals = 0;

    @Column(nullable = false)
    private Integer totalProducts = 0;

    @Column(nullable = false)
    private Integer totalBarters = 0;

    @Column(nullable = false)
    private Double averageRating = 0.0;

    @Column(nullable = false)
    private Integer totalReviews = 0;

    // Account Status
    @Column(nullable = false)
    private Boolean isActive = true;

    @Column(nullable = false)
    private Boolean isEmailVerified = false;

    @Enumerated(EnumType.STRING)
    private Role role = Role.USER;

    // Relationships
    @OneToMany(mappedBy = "owner", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Product> products = new ArrayList<>();

    @OneToMany(mappedBy = "bidder", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Bid> bids = new ArrayList<>();

    @OneToMany(mappedBy = "reviewer", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Review> reviewsGiven = new ArrayList<>();

    @OneToMany(mappedBy = "reviewee", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Review> reviewsReceived = new ArrayList<>();

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Notification> notifications = new ArrayList<>();

    // Audit fields
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    private LocalDateTime updatedAt;

    // Enums
    public enum Role {
        USER, ADMIN, MODERATOR
    }

    public enum UserRank {
        BEGINNER("مبتدئ"),
        PROFESSIONAL("محترف"), 
        PREMIUM("مميز"),
        KING("ملك المزايضة");

        private final String arabicName;

        UserRank(String arabicName) {
            this.arabicName = arabicName;
        }

        public String getArabicName() {
            return arabicName;
        }
    }

    // Helper methods
    public void addPoints(int points) {
        this.points += points;
        updateRank();
    }

    private void updateRank() {
        if (points >= 1000) {
            this.rank = UserRank.KING;
        } else if (points >= 500) {
            this.rank = UserRank.PREMIUM;
        } else if (points >= 200) {
            this.rank = UserRank.PROFESSIONAL;
        } else {
            this.rank = UserRank.BEGINNER;
        }
    }

    public void updateAverageRating() {
        if (totalReviews > 0) {
            double sum = reviewsReceived.stream()
                    .mapToDouble(Review::getRating)
                    .sum();
            this.averageRating = sum / totalReviews;
        }
    }
}
